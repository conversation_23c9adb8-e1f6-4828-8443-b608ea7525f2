package ai.yourouter.common.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.regex.Pattern;

@Component
@Slf4j
public class JsonUtils implements ApplicationContextAware {

    static public ObjectMapper mapper;

    @SneakyThrows
    public static JsonNode parseJson(String body) {
        return mapper.readTree(body);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        mapper = applicationContext.getBean(ObjectMapper.class);
    }

    public static String toJSONString(Object object) {
        try {
            return mapper.writeValueAsString(object);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T parseObject(String json, Class<T> clazz) {
        try {
            return mapper.readValue(json, clazz);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @SneakyThrows
    public static String removeField(String jsonString, String... fieldsToRemove) {
        try {
            JsonNode jsonNode = mapper.readTree(jsonString);
            removeFieldsRecursively(jsonNode, fieldsToRemove);
            return mapper.writeValueAsString(jsonNode);
        } catch (JsonProcessingException e) {
            log.error("Error removing fields from JSON", e);
            return jsonString;
        }
    }

    private static void removeFieldsRecursively(JsonNode node, String... fieldsToRemove) {
        if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node;
            for (String field : fieldsToRemove) {
                objectNode.remove(field);
            }

            Iterator<JsonNode> elements = objectNode.elements();
            while (elements.hasNext()) {
                removeFieldsRecursively(elements.next(), fieldsToRemove);
            }
        } else if (node.isArray()) {
            for (JsonNode arrayElement : node) {
                removeFieldsRecursively(arrayElement, fieldsToRemove);
            }
        }
    }

    public static boolean isArrayJson(String str) {
        return Pattern.matches("\\[.*\\]", str);
    }

}

