package ai.yourouter.chat.controller;

import ai.yourouter.chat.service.impl.GeminiMessagesServiceImpl;
import ai.yourouter.chat.util.ResponseUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.CorePublisher;

import java.util.HashMap;

/**
 * Gemini 原生 API 控制器
 * 处理 Gemini 原生格式的请求
 */
@Slf4j
@RestController
@RequestMapping("/v1/projects/cognition/locations/us/publishers/google/models")
@RequiredArgsConstructor
@SuppressWarnings("ReactiveStreamsUnusedPublisher")
public class GeminiMessagesController {

    private final GeminiMessagesServiceImpl geminiMessagesService;

    /**
     * Gemini generateContent API 端点 (非流式)
     * 支持原生 Gemini API 格式
     */
    @PostMapping("/{model}:generateContent")
    public ResponseEntity<? extends CorePublisher<?>> generateContent(
            @PathVariable String model,
            @RequestBody HashMap<String, Object> req) {
        return buildGeminiNonStreamResponse(req);
    }

    /**
     * Gemini streamGenerateContent API 端点 (流式)
     * 支持原生 Gemini API 格式
     */
    @PostMapping("/{model}:streamGenerateContent")
    public ResponseEntity<? extends CorePublisher<?>> streamGenerateContent(
            @PathVariable String model,
            @RequestBody HashMap<String, Object> req) {
        return buildGeminiStreamResponse(req);
    }

    /**
     * 构建 Gemini 流式响应
     */
    private ResponseEntity<? extends CorePublisher<?>> buildGeminiStreamResponse(HashMap<String, Object> req) {

        return ResponseUtils.createStreamResponse(geminiMessagesService.streamCompletion(req));
    }

    /**
     * 构建 Gemini 非流式响应
     */
    private ResponseEntity<? extends CorePublisher<?>> buildGeminiNonStreamResponse(HashMap<String, Object> req) {
        return ResponseUtils.createNonStreamResponse(geminiMessagesService.completion(req));
    }

}
