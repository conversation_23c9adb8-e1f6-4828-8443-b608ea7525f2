package ai.yourouter.chat.channel;

import ai.yourouter.chat.channel.aws.req.BedrockRequest;
import ai.yourouter.chat.channel.response.claude.ClaudeMessageTokenStats;
import ai.yourouter.chat.channel.response.claude.SystemPrompt;
import ai.yourouter.chat.remote.KmgRemoteService;
import ai.yourouter.chat.remote.response.BestKeyResponse;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.context.usage.ChatUsage;
import ai.yourouter.common.exception.CognitionWebException;
import ai.yourouter.common.utils.BedrockApiInvoker;
import ai.yourouter.common.utils.GatewayHelperUtils;
import ai.yourouter.common.utils.JsonUtils;
import ai.yourouter.common.utils.TraceUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Base64;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;

/**
 * Claude 远程服务
 * 处理与 Claude API 的通信，包括流式和非流式请求
 */
@Slf4j
@Service
@SuppressWarnings({"DuplicatedCode", "unused", "LoggingSimilarMessage"})
public class ClaudeRemoteService extends LlmRemoteService {

    // 常量定义
    private static final String CLAUDE_MESSAGES_PATH = "/v1/messages";
    private static final String STREAM_DONE_MARKER = "[DONE]";
    private static final String BEARER_PREFIX = "Bearer ";
    private static final String MODEL_FIELD = "model";
    private static final String ANTHROPIC_VERSION_HEADER = "anthropic-version";
    private static final String ANTHROPIC_VERSION_VALUE = "2023-06-01";

    // AWS Bedrock 相关常量
    private static final String AWS_STREAM_URI = "https://bedrock-runtime.%s.amazonaws.com/model/%s/invoke-with-response-stream";
    private static final String AWS_NON_STREAM_URI = "https://bedrock-runtime.%s.amazonaws.com/model/%s/invoke";
    private static final String AWS_ANTHROPIC_VERSION = "bedrock-2023-05-31";

    @Qualifier("httpWebClient")
    private final WebClient httpWebClient;

    @Qualifier("sseWebClient")
    private final WebClient sseWebClient;

    private final ObjectMapper objectMapper;

    public ClaudeRemoteService(KmgRemoteService kmgRemoteService,
                               @Qualifier("httpWebClient") WebClient httpWebClient,
                               @Qualifier("sseWebClient") WebClient sseWebClient, ObjectMapper objectMapper) {
        super(kmgRemoteService);
        this.httpWebClient = httpWebClient;
        this.sseWebClient = sseWebClient;
        this.objectMapper = objectMapper;
    }

    /**
     * 构建 Claude API 请求的域名 URL
     *
     * @param chatContext 聊天上下文
     * @return 完整的 API URL
     */
    private String buildApiUrl(ChatContext chatContext) {
        BestKeyResponse keyInfo = chatContext.getKeyInfo();
        if (keyInfo == null) {
            throw new CognitionWebException("Key info is null in chat context");
        }

        String domain = keyInfo.getDomain();
        if (StringUtils.hasLength(domain)) {
            return domain + CLAUDE_MESSAGES_PATH;
        } else {
            return "https://api.anthropic.com" + CLAUDE_MESSAGES_PATH;
        }
    }

    /**
     * URL编码工具方法
     */
    private static String encodeURL(String url) {
        return URLEncoder.encode(url, StandardCharsets.UTF_8);
    }

    /**
     * 为AWS Bedrock请求添加认证头
     */
    private static void addAwsResponseHeader(String body, ChatContext chatContext, WebClient.RequestBodySpec requestBodyUriSpec, String url) {
        var timestamp = ZonedDateTime.now(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'"));
        var keyInfo = chatContext.getKeyInfo();
        String secret = keyInfo.getSecret();
        String accessKey = keyInfo.getToken();
        String region = keyInfo.getRegion();

        var request = new BedrockRequest(
                accessKey,
                secret,
                region,
                "bedrock",
                body,
                timestamp,
                UUID.randomUUID().toString()
        );
        var response = BedrockApiInvoker.invokeBedrockApi(request, url);
        response.getHeaders().forEach(requestBodyUriSpec::header);
    }

    /**
     * 准备 Claude 请求体
     *
     * @param body        原始请求体
     * @param chatContext 聊天上下文
     */
    private void prepareClaudeRequestBody(HashMap<String, Object> body, ChatContext chatContext) {
        BestKeyResponse keyInfo = chatContext.getKeyInfo();

        if (keyInfo.onAws()) {
            // AWS Bedrock 请求体处理
            body.remove("model");
            body.remove("stream");
            body.put("anthropic_version", AWS_ANTHROPIC_VERSION);
        } else {
            // 标准 Claude API 使用原始模型名称，不需要添加前缀
            body.put(MODEL_FIELD, chatContext.apiModelName());
        }
    }

    /**
     * 流式 Claude 消息完成
     */
    public Flux<ServerSentEvent<String>> streamClaudeCompletion(HashMap<String, Object> body, ChatContext chatContext) {
        var isFirst = new AtomicBoolean(true);
        prepareClaudeRequestBody(body, chatContext);

        return Mono.just(chatContext.getKeyInfo())
                .flatMapMany(key -> createStreamRequest(body, chatContext, key, isFirst));
    }

    /**
     * 创建流式请求
     */
    private Flux<ServerSentEvent<String>> createStreamRequest(HashMap<String, Object> body,
                                                              ChatContext chatContext,
                                                              BestKeyResponse key,
                                                              AtomicBoolean isFirst) {
        if (key.onAws()) {
            return createAwsStreamRequest(body, chatContext, key, isFirst);
        } else {
            return createStandardStreamRequest(body, chatContext, key, isFirst);
        }
    }

    /**
     * 创建标准Claude流式请求
     */
    private Flux<ServerSentEvent<String>> createStandardStreamRequest(HashMap<String, Object> body,
                                                                      ChatContext chatContext,
                                                                      BestKeyResponse key,
                                                                      AtomicBoolean isFirst) {
        String apiUrl = buildApiUrl(chatContext);

        var headers = sseWebClient.post()
                .uri(apiUrl)
                .header("anthropic-version", chatContext.getChatRequestStatistic().getRequestHeaders().getOrDefault("anthropic-version", "2023-06-01"));
        if (chatContext.getChatRequestStatistic().getRequestHeaders().containsKey("anthropic-beta")) {
            headers.header("anthropic-beta", chatContext.getChatRequestStatistic().getRequestHeaders().get("anthropic-beta"));
        }
        if (key.onClaudeCode()) {
            headers.header("authorization", "Bearer " + key.getToken());
            setClaudeCodeSpecificHeaders(headers, chatContext);
            body = processSystemPrompts(body);
        } else {
            headers.header("x-api-key", key.getToken());
        }
        return headers
                .bodyValue(body)
                .retrieve()
                .bodyToFlux(String.class)
                .map(processStream(chatContext, isFirst))
                .map(response -> (ServerSentEvent.<String>builder().data(response._1).event(response._2).build()))
                .publishOn(Schedulers.boundedElastic())
                .doOnError(throwable -> handleWebClientError(throwable, key, chatContext))
                .doOnComplete(() -> handleApiResult(key, chatContext, true));
    }

    /**
     * 创建AWS Bedrock流式请求
     */
    private Flux<ServerSentEvent<String>> createAwsStreamRequest(HashMap<String, Object> body,
                                                                 ChatContext chatContext,
                                                                 BestKeyResponse key,
                                                                 AtomicBoolean isFirst) {
        String region = key.getRegion();
        var uri = String.format(AWS_STREAM_URI, region, encodeURL(chatContext.apiModelName()));
        var req = JsonUtils.toJSONString(body);

        WebClient.RequestBodySpec clientReqSpec;
        try {
            clientReqSpec = sseWebClient.post().uri(new URI(uri));
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }

        addAwsResponseHeader(req, chatContext, clientReqSpec, uri);

        return clientReqSpec
                .bodyValue(req)
                .retrieve()
                .bodyToFlux(byte[].class)
                .map(bytes -> new String(bytes, StandardCharsets.UTF_8))
                .scan(new StringBuilder(), (acc, value) -> {
                    acc.append(value);
                    int newlineIndex;
                    while ((newlineIndex = acc.indexOf("\r")) != -1) {
                        String line = acc.substring(0, newlineIndex);
                        acc.delete(0, newlineIndex + 1);
                        if (!line.isBlank()) {
                            if (line.contains(":message-type")) {
                                String content = parseEventContent(line);
                                return new StringBuilder(content);
                            }
                        }
                    }
                    return acc;
                })
                .filter(sb -> sb.length() > 0)
                .map(StringBuilder::toString)
                .map(processStream(chatContext, isFirst))
                .map(response -> (ServerSentEvent.<String>builder().data(response._1).event(response._2).build()))
                .publishOn(Schedulers.boundedElastic())
                .doOnError(throwable -> handleWebClientError(throwable, key, chatContext))
                .doOnComplete(() -> handleApiResult(key, chatContext, true));
    }

    /**
     * 解析AWS事件内容
     */
    private static String parseEventContent(String content) {
        try {
            var jsonContent = getString(content);
            var jsonNode = JsonUtils.parseJson(jsonContent);
            String bytesBase64 = jsonNode.get("bytes").asText();
            byte[] decodedBytes = Base64.getDecoder().decode(bytesBase64);
            return new String(decodedBytes);
        } catch (Exception e) {
            log.error("解析AWS事件内容时发生错误: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 提取JSON字符串
     */
    private static String getString(String content) {
        int jsonStart = content.indexOf("{");
        int bracketCount = 0;
        int jsonEnd = jsonStart;
        for (int i = jsonStart; i < content.length(); i++) {
            if (content.charAt(i) == '{') {
                bracketCount++;
            } else if (content.charAt(i) == '}') {
                bracketCount--;
            }
            if (bracketCount == 0) {
                jsonEnd = i + 1;
                break;
            }
        }
        return content.substring(jsonStart, jsonEnd);
    }

    /**
     * 处理 Claude 流式响应数据
     */
    private Function<String, Tuple2<String, String>> processStream(ChatContext chatContext, AtomicBoolean isFirst) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {

            if (isFirst.getAndSet(false)) {
                chatContext.getChatRequestStatistic().setPreparationTime(Instant.now().toEpochMilli());
                log.debug("收到首个数据块 | 用户ID: {} | 模型: {}", userId, modelName);
            }

            // 收集流式响应内容到 streamResponse
            if (message != null && !message.trim().isEmpty()) {
                chatContext.getChatRequestStatistic().getStreamResponse().append(message).append("\n");
            }
            try {

                // Log detailed response at DEBUG level to avoid excessive logging
                log.debug("接收数据块 | 用户ID: {} | 模型: {} | 消息内容: {}", userId, modelName, message);

                var jsonNode = JsonUtils.parseJson(message);
                var type = jsonNode.path("type").asText();
                if ("message_start".equalsIgnoreCase(type)) {
                    var usage = objectMapper.convertValue(jsonNode.path("message").path("usage"), ClaudeMessageTokenStats.class);
                    chatContext.setChatUsage(ChatUsage.create(usage));
                    log.info("更新使用情况 | 请求类型: 流式 | 用户ID: {} | 模型: {} | 使用量: {}", userId, modelName, JsonUtils.toJSONString(usage));
                }

                if ("message_delta".equalsIgnoreCase(type)) {
                    var outputTokens = jsonNode.path("usage").path("output_tokens").asInt();
                    chatContext.getChatUsage().updateOutPut(outputTokens);
                }

                log.debug("数据块解析完成 | 用户ID: {} | 模型: {} | 响应内容: {}",
                        userId, modelName, message);

                // 使用父类的Claude特殊处理方法添加vendor和ID信息
                String processedMessage = processClaudeStreamWithVendorAndId(message, chatContext);

                var messages = objectMapper.readValue(processedMessage, LinkedHashMap.class);
                return Tuple.of(objectMapper.writeValueAsString(messages), type);
            } catch (Exception e) {
                log.error("数据块解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}",
                        userId, modelName, e.getMessage());
                throw new CognitionWebException("Error occurred with OpenAI API stream parse to chatCompletionChunk: " + e.getMessage());
            }
        };
    }

    /**
     * 非流式 Claude 消息完成
     */
    public Mono<Object> nonStreamClaudeCompletion(HashMap<String, Object> body, ChatContext chatContext) {
        prepareClaudeRequestBody(body, chatContext);

        return Mono.just(chatContext.getKeyInfo())
                .flatMap(key -> createNonStreamRequest(body, chatContext, key));
    }

    /**
     * 创建非流式请求
     */
    private Mono<Object> createNonStreamRequest(HashMap<String, Object> body,
                                                ChatContext chatContext,
                                                BestKeyResponse key) {
        if (key.onAws()) {
            return createAwsNonStreamRequest(body, chatContext, key);
        } else {
            return createStandardNonStreamRequest(body, chatContext, key);
        }
    }

    /**
     * 创建标准Claude非流式请求
     */
    private Mono<Object> createStandardNonStreamRequest(HashMap<String, Object> body,
                                                        ChatContext chatContext,
                                                        BestKeyResponse key) {
        String apiUrl = buildApiUrl(chatContext);

        var headers = httpWebClient.post()
                .uri(apiUrl)
                .header("anthropic-version", chatContext.getChatRequestStatistic().getRequestHeaders().getOrDefault("anthropic-version", "2023-06-01"));
        if (chatContext.getChatRequestStatistic().getRequestHeaders().containsKey("anthropic-beta")) {
            headers.header("anthropic-beta", chatContext.getChatRequestStatistic().getRequestHeaders().get("anthropic-beta"));
        }
        if (key.onClaudeCode()) {
            headers.header("authorization", "Bearer " + key.getToken());
            setClaudeCodeSpecificHeaders(headers, chatContext);
            body = processSystemPrompts(body);
        } else {
            headers.header("x-api-key", key.getToken());
        }
        return headers
                .bodyValue(body)
                .retrieve()
                .bodyToMono(String.class)
                .map(processNonStream(chatContext))
                .publishOn(Schedulers.boundedElastic())
                .doOnError(throwable -> handleWebClientError(throwable, key, chatContext))
                .doOnSuccess(result -> handleApiResult(key, chatContext, true));
    }

    /**
     * 创建AWS Bedrock非流式请求
     */
    private Mono<Object> createAwsNonStreamRequest(HashMap<String, Object> body,
                                                   ChatContext chatContext,
                                                   BestKeyResponse key) {
        String region = key.getRegion();
        var uri = String.format(AWS_NON_STREAM_URI, region, encodeURL(chatContext.apiModelName()));
        var req = JsonUtils.toJSONString(body);

        WebClient.RequestBodySpec clientReqSpec;
        try {
            clientReqSpec = httpWebClient.post().uri(new URI(uri));
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }

        addAwsResponseHeader(req, chatContext, clientReqSpec, uri);

        return clientReqSpec
                .bodyValue(req)
                .retrieve()
                .bodyToMono(String.class)
                .doOnCancel(() -> {
                    log.warn("AWS请求被取消或超时");
                    throw new RuntimeException("AWS请求被取消或超时");
                })
                .map(processNonStream(chatContext))
                .publishOn(Schedulers.boundedElastic())
                .doOnError(throwable -> handleWebClientError(throwable, key, chatContext))
                .doOnSuccess(result -> handleApiResult(key, chatContext, true));
    }

    /**
     * 处理 Claude 非流式响应数据
     */
    private Function<String, Object> processNonStream(ChatContext chatContext) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {
            try {
                log.debug("接收非流式响应 | 用户ID: {} | 模型: {} | 消息内容: {}",
                        userId, modelName, message);
                var usage = objectMapper.convertValue(JsonUtils.parseJson(message).path("usage"), ClaudeMessageTokenStats.class);

                if (usage != null) {
                    chatContext.setChatUsage(ChatUsage.create(usage));
                    log.info("更新使用情况 | 请求类型: 非流式 | 用户ID: {} | 模型: {} | 使用量: {}", userId, modelName, JsonUtils.toJSONString(usage));
                }

                // 使用父类的共性方法添加vendor和ID信息
                return processNonStreamWithVendorAndId(message, chatContext);
            } catch (Exception e) {
                log.error("响应解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}",
                        userId, modelName, e.getMessage());
                throw new CognitionWebException("Error occurred with Claude API stream parse to chatCompletion: " + e.getMessage());
            }
        };
    }


    private void setClaudeCodeSpecificHeaders(WebClient.RequestHeadersSpec<?> requestHeadersSpec, ChatContext chatContext) {
        requestHeadersSpec.header("x-app", "cli")
                .header("User-Agent", "claude-cli/1.0.41 (external, cli)")
                .header(
                        "anthropic-beta",
                        "claude-code-20250219,oauth-2025-04-20,interleaved-thinking-2025-05-14,fine-grained-tool-streaming-2025-05-14"
                );
    }

    public HashMap<String, Object> processSystemPrompts(HashMap<String, Object> request) {

        if (!request.containsKey("system")) {
            List<SystemPrompt> processedPrompts = new ArrayList<>();
            processedPrompts.add(SystemPrompt.createDefault());
            request.put("system", processedPrompts);
            return request;
        }

        Object systemObj = request.get("system");
        List<SystemPrompt> processedPrompts = new ArrayList<>();

        // 添加默认的Claude系统提示
        processedPrompts.add(SystemPrompt.createDefault());

        if (systemObj instanceof String systemText) {
            // 如果是字符串，创建文本提示
            processedPrompts.add(SystemPrompt.createTextPrompt(systemText));
            log.debug("处理字符串类型system prompt - 长度: {}", systemText.length());
        } else if (systemObj instanceof List<?> systemList) {
            // 如果是列表，处理每个提示
            List<SystemPrompt> userPrompts = systemList.stream()
                    .filter(item -> item instanceof Map)
                    .map(item -> {
                        @SuppressWarnings("unchecked")
                        Map<String, String> promptMap = (Map<String, String>) item;
                        return SystemPrompt.createCustomPrompt(
                                promptMap.get("type"),
                                promptMap.get("text")
                        );
                    })
                    .toList();
            processedPrompts.addAll(userPrompts);
            log.debug("处理列表类型system prompts - 数量: {}", userPrompts.size());
        }

        request.put("system", processedPrompts);
        log.debug("System prompts处理完成 - 总数: {}", processedPrompts.size());
        return request;
    }
}
