package ai.yourouter.chat.remote.response;// BestKeyResponse.java

import lombok.Data;

import java.util.Map;

@Data
public class BestKeyResponse {
    private Long id;
    private String token;
    private String channel;
    private Map<String, String> modelMapping;
    private String name;
    private String domain;
    private String selectedAt;
    private Map<String, String> metadata;


    public boolean onClaudeCode() {
        return channel.equalsIgnoreCase("claudecode");
    }

    public boolean onAzure() {
        return channel.equalsIgnoreCase("azure");
    }

    public boolean onAws() {
        return channel.equalsIgnoreCase("aws");
    }

    public String getRegion() {
        if (metadata != null) {
            return metadata.get("region");
        }
        return null;
    }

    public String getSecret() {
        if (metadata != null) {
            return metadata.get("secret");
        }
        return null;
    }
}
